'use client'

import React, { ReactNode } from 'react'
import { useSecurePermission, useSecureSuperAdminCheck } from '@/hooks/useSecurePermission'

interface SecureAuthWrapperProps {
  /**
   * The roles that are allowed to see this component
   */
  allowedRoles?: string[]

  /**
   * The feature this component is part of
   */
  feature?: 'templates' | 'documents' | 'cases' | 'admin'

  /**
   * Whether super admin access is required
   */
  requireSuperAdmin?: boolean

  /**
   * Whether to show a fallback component when the user doesn't have access
   */
  showFallback?: boolean

  /**
   * Custom fallback component to show when the user doesn't have access
   */
  fallback?: ReactNode

  /**
   * Custom loading component to show while checking permissions
   */
  loadingComponent?: ReactNode

  /**
   * The content to render when the user has access
   */
  children: ReactNode

  /**
   * Custom error message for unauthorized access
   */
  unauthorizedMessage?: string
}

/**
 * SecureAuthWrapper - High-security authorization component
 *
 * This component ALWAYS uses server-side validation and cannot be bypassed.
 * Use this for critical operations, sensitive data, and admin functions.
 *
 * Features:
 * - Mandatory server-side permission validation
 * - Cannot be bypassed by client-side manipulation
 * - Comprehensive loading and error states
 * - Flexible permission checking (roles, features, super admin)
 *
 * For non-critical UI elements where performance is prioritized over security,
 * use RoleBasedComponent with useServerValidation=false.
 */
export function SecureAuthWrapper({
  allowedRoles,
  feature,
  requireSuperAdmin = false,
  showFallback = false,
  fallback = null,
  loadingComponent = null,
  children,
  unauthorizedMessage = 'You do not have permission to access this content.'
}: SecureAuthWrapperProps) {
  // Use appropriate secure permission hook based on requirements
  const permissionCheck = requireSuperAdmin
    ? useSecureSuperAdminCheck()
    : useSecurePermission({ roles: allowedRoles, feature });

  // Show loading state while checking permissions
  if (permissionCheck.isLoading) {
    if (loadingComponent) {
      return <>{loadingComponent}</>;
    }

    return (
      <div className="flex items-center justify-center p-4">
        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900"></div>
        <span className="ml-2 text-sm text-gray-600">Verifying permissions...</span>
      </div>
    );
  }

  // Handle permission errors
  if (permissionCheck.error) {
    console.error('SecureAuthWrapper: Permission check error:', permissionCheck.error);
    
    if (showFallback && fallback) {
      return <>{fallback}</>;
    }

    return (
      <div className="p-4 bg-red-50 border border-red-200 rounded-md">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-red-800">
              Permission Check Failed
            </h3>
            <div className="mt-2 text-sm text-red-700">
              <p>Unable to verify your permissions. Please try again or contact support.</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Check if user has access
  if (!permissionCheck.hasAccess) {
    if (showFallback && fallback) {
      return <>{fallback}</>;
    }

    return (
      <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-md">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-yellow-800">
              Access Denied
            </h3>
            <div className="mt-2 text-sm text-yellow-700">
              <p>{unauthorizedMessage}</p>
              {permissionCheck.userRole && (
                <p className="mt-1">Your current role: {permissionCheck.userRole}</p>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  }

  // User has access, render children
  return <>{children}</>;
}

/**
 * Convenience wrapper for super admin only content
 */
export function SecureSuperAdminWrapper({
  children,
  showFallback = false,
  fallback = null,
  loadingComponent = null,
  unauthorizedMessage = 'Super admin access required.'
}: Omit<SecureAuthWrapperProps, 'allowedRoles' | 'feature' | 'requireSuperAdmin'>) {
  return (
    <SecureAuthWrapper
      requireSuperAdmin={true}
      showFallback={showFallback}
      fallback={fallback}
      loadingComponent={loadingComponent}
      unauthorizedMessage={unauthorizedMessage}
    >
      {children}
    </SecureAuthWrapper>
  );
}

/**
 * Convenience wrapper for admin (partner) only content
 */
export function SecureAdminWrapper({
  children,
  showFallback = false,
  fallback = null,
  loadingComponent = null,
  unauthorizedMessage = 'Admin access required.'
}: Omit<SecureAuthWrapperProps, 'allowedRoles' | 'feature' | 'requireSuperAdmin'>) {
  return (
    <SecureAuthWrapper
      allowedRoles={['partner']}
      showFallback={showFallback}
      fallback={fallback}
      loadingComponent={loadingComponent}
      unauthorizedMessage={unauthorizedMessage}
    >
      {children}
    </SecureAuthWrapper>
  );
}

export default SecureAuthWrapper;
