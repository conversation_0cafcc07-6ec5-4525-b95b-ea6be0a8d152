'use client'

import React, { ReactNode } from 'react'
import { useRbac } from '@/hooks/useRbac'
import { useUser } from '@/contexts/UserContext'
import { useSecurePermission } from '@/hooks/useSecurePermission'

interface RoleBasedComponentProps {
  /**
   * The roles that are allowed to see this component
   */
  allowedRoles?: string[]

  /**
   * The feature this component is part of
   * Will use the hasAccess method from useRbac
   */
  feature?: 'templates' | 'documents' | 'cases' | 'admin'

  /**
   * Whether to show a fallback component when the user doesn't have access
   */
  showFallback?: boolean

  /**
   * Custom fallback component to show when the user doesn't have access
   */
  fallback?: ReactNode

  /**
   * The content to render when the user has access
   */
  children: ReactNode

  /**
   * Whether to use server-side validation for enhanced security
   * When enabled, permissions are validated server-side and cannot be bypassed
   * Default: true (for maximum security)
   */
  useServerValidation?: boolean
}

/**
 * Component that conditionally renders content based on user role
 *
 * SECURITY ENHANCEMENT: Now uses server-side validation by default for critical security
 * Provides defense-in-depth security:
 * 1. Client-side metadata validation for fast UX (can be bypassed but provides good UX)
 * 2. Server-side validation for critical security (cannot be bypassed)
 *
 * Server validation is enabled by default for maximum security.
 * Set useServerValidation=false only for non-critical UI elements where performance is prioritized.
 */
export function RoleBasedComponent({
  allowedRoles,
  feature,
  showFallback = false,
  fallback = null,
  useServerValidation = true, // SECURITY: Changed default to true for enhanced security
  children
}: RoleBasedComponentProps) {
  const rbac = useRbac()
  const { loading } = useUser()

  // Use server-side validation for enhanced security if requested
  const serverValidation = useSecurePermission({
    roles: allowedRoles,
    feature,
  })

  // If still loading (client or server), don't render anything yet
  const isLoading = loading || (useServerValidation && serverValidation.isLoading);
  if (isLoading) {
    return null
  }

  // Check client-side access
  let hasClientAccess = false;
  if (feature && rbac.hasAccess(feature)) {
    hasClientAccess = true;
  } else if (allowedRoles && rbac.hasRole(allowedRoles)) {
    hasClientAccess = true;
  }

  // Check server-side access if enabled
  const hasServerAccess = useServerValidation ? serverValidation.hasAccess : true;

  // User must have both client and server access (if server validation is enabled)
  const hasAccess = hasClientAccess && hasServerAccess;

  if (hasAccess) {
    return <>{children}</>
  }

  // If no access and fallback is enabled, show fallback
  if (showFallback) {
    return <>{fallback || <AccessDeniedFallback />}</>
  }

  // Otherwise render nothing (secure default)
  return null
}

/**
 * Default fallback component when access is denied
 */
function AccessDeniedFallback() {
  return (
    <div className="p-4 border border-red-200 rounded bg-red-50 text-red-800">
      <h3 className="font-semibold">Access Denied</h3>
      <p>You don&apos;t have permission to view this content.</p>
    </div>
  )
}

/**
 * Higher-order component that wraps a component with role-based access control
 */
export function withRoleBasedAccess<P extends object>(
  Component: React.ComponentType<P>,
  options: Omit<RoleBasedComponentProps, 'children'>
) {
  return function WithRoleBasedAccess(props: P) {
    return (
      <RoleBasedComponent {...options}>
        <Component {...props} />
      </RoleBasedComponent>
    )
  }
}
