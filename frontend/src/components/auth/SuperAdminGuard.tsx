'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useRbac } from '@/hooks/useRbac';
import { useUser } from '@/contexts/UserContext';
import { useSecureSuperAdminCheck } from '@/hooks/useSecurePermission';

interface SuperAdminGuardProps {
  children: React.ReactNode;
  fallbackPath?: string;
  showLoading?: boolean;
  useServerValidation?: boolean; // Enhanced security option
}

/**
 * SuperAdminGuard - Higher Order Component for protecting super admin routes
 *
 * SECURITY ENHANCEMENT: Now uses server-side validation by default for critical security
 * This component provides defense-in-depth security by:
 * 1. Client-side metadata checking for fast UX (can be bypassed but provides good UX)
 * 2. Server-side validation for critical security (cannot be bypassed)
 *
 * Server validation is enabled by default for maximum security.
 * Set useServerValidation=false only for non-critical UI elements where performance is prioritized.
 *
 * @param children - The components to render if user has super admin access
 * @param fallbackPath - Path to redirect to if user lacks super admin access (default: '/')
 * @param showLoading - Whether to show loading state while checking permissions (default: true)
 * @param useServerValidation - Whether to use server-side validation for enhanced security (default: true)
 */
export function SuperAdminGuard({
  children,
  fallbackPath = '/',
  showLoading = true,
  useServerValidation = true // SECURITY: Changed default to true for enhanced security
}: SuperAdminGuardProps) {
  const { isSuperAdmin, isLoading } = useRbac();
  const { loading: userLoading } = useUser();
  const router = useRouter();

  // Use server-side validation for enhanced security if requested
  const serverValidation = useSecureSuperAdminCheck();

  const isCheckingAuth = isLoading() || userLoading ||
    (useServerValidation && serverValidation.isLoading);

  useEffect(() => {
    // Only redirect if we&apos;re done loading and user is not a super admin
    const hasClientAccess = isSuperAdmin();
    const hasServerAccess = useServerValidation ? serverValidation.hasAccess : true;

    if (!isCheckingAuth && (!hasClientAccess || !hasServerAccess)) {
      console.warn('SuperAdminGuard: Unauthorized access attempt to super admin route. Redirecting to:', fallbackPath);
      console.warn('Client access:', hasClientAccess, 'Server access:', hasServerAccess);
      router.replace(fallbackPath);
    }
  }, [isCheckingAuth, isSuperAdmin, serverValidation.hasAccess, useServerValidation, router, fallbackPath]);

  // Show loading state while checking authentication
  if (isCheckingAuth && showLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900"></div>
        <p className="mt-4 text-sm text-gray-600">
          {useServerValidation ? 'Verifying admin privileges...' : 'Loading...'}
        </p>
      </div>
    );
  }

  // Check both client and server validation if server validation is enabled
  const hasClientAccess = isSuperAdmin();
  const hasServerAccess = useServerValidation ? serverValidation.hasAccess : true;

  // Don't render children if user doesn't have access
  if (!hasClientAccess || !hasServerAccess) {
    return null;
  }

  // Render children if user has super admin access
  return <>{children}</>;
}

export default SuperAdminGuard;
