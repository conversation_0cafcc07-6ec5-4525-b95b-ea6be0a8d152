'use client'

import React, { useState } from 'react'
import { useUser } from '@/contexts/UserContext'
import { useRbac } from '@/hooks/useRbac'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'

/**
 * Debug component that displays user role information
 * SECURITY: JWT payload display removed for security hardening
 * Only shown in development mode
 */
export function JwtDebugger() {
  const { user, role, tenantId } = useUser()
  const rbac = useRbac()
  const [expanded, setExpanded] = useState(false)

  // Only show in development mode
  if (process.env.NODE_ENV !== 'development') {
    return null
  }

  return (
    <Card className="border-dashed border-yellow-300 bg-yellow-50 mb-6">
      <CardHeader className="pb-2">
        <CardTitle className="text-sm font-medium flex items-center justify-between">
          <span>JWT & Role Debugger</span>
          <Badge variant="outline" className={role ? 'bg-green-100' : 'bg-red-100'}>
            {role || 'No Role'}
          </Badge>
        </CardTitle>
        <CardDescription className="text-xs">
          Development tool to inspect user role and metadata (JWT payload removed for security)
        </CardDescription>
      </CardHeader>

      <CardContent className="text-xs pb-2">
        <div className="grid grid-cols-2 gap-2">
          <div>
            <p className="font-semibold">User ID:</p>
            <p className="font-mono">{user?.id || 'Not logged in'}</p>
          </div>
          <div>
            <p className="font-semibold">Role:</p>
            <p className="font-mono">{role || 'None'}</p>
          </div>
          <div>
            <p className="font-semibold">Tenant ID:</p>
            <p className="font-mono">{tenantId || 'None'}</p>
          </div>
          <div>
            <p className="font-semibold">RBAC Status:</p>
            <p className="font-mono">
              {rbac.isAdmin() ? 'Admin' : rbac.isStaff() ? 'Staff' : rbac.isClient() ? 'Client' : 'Unknown'}
            </p>
          </div>
        </div>

        {expanded && (
          <div className="mt-4 border-t pt-2">
            <p className="font-semibold mb-1">User Metadata:</p>
            <pre className="bg-gray-100 p-2 rounded text-[10px] overflow-auto max-h-40">
              {JSON.stringify({
                app_metadata: user?.app_metadata,
                user_metadata: user?.user_metadata,
                email: user?.email,
                id: user?.id
              }, null, 2)}
            </pre>
            <p className="text-xs text-gray-600 mt-2">
              ⚠️ JWT payload access removed for security. Using metadata-based authorization.
            </p>
          </div>
        )}
      </CardContent>

      <CardFooter className="pt-0">
        <Button
          variant="outline"
          size="sm"
          className="text-xs h-7 w-full"
          onClick={() => setExpanded(!expanded)}
        >
          {expanded ? 'Hide Details' : 'Show User Details'}
        </Button>
      </CardFooter>
    </Card>
  )
}
