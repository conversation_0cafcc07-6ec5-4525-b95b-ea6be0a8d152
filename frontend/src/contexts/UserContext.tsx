'use client'

import React, { createContext, useContext } from 'react'
import { useSession } from '@/contexts/SessionContext'
import { User } from '@supabase/supabase-js';

// SECURITY: Removed JwtPayload interface and jwtPayload property
// Client-side JWT parsing has been eliminated for security

// Define the shape of our user context
interface UserContextType {
  user: User | null
  loading: boolean
  role: string | null
  tenantId: string | null
  refreshUser: () => Promise<void>
  // SECURITY: jwtPayload removed - use server-side validation instead
}

// Create context with default values
const UserContext = createContext<UserContextType>({
  user: null,
  loading: true,
  role: null,
  tenantId: null,
  refreshUser: async () => {},
})

// Custom hook for easy use of the context
export const useUser = () => useContext(UserContext)

export const UserProvider = ({ children }: { children: React.ReactNode }) => {
  const { session, user } = useSession()
  // SECURITY: Use secure metadata instead of client-side JWT parsing
  const loading = session === null
  const role = user?.app_metadata?.role || user?.user_metadata?.role || null
  const tenantId = user?.app_metadata?.tenant_id || user?.user_metadata?.tenant_id || null

  return (
    <UserContext.Provider
      value={{ user, loading, role, tenantId, refreshUser: async () => {} }}
    >
      {children}
    </UserContext.Provider>
  )
}
