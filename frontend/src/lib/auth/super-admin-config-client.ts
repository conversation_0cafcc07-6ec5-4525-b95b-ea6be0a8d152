/**
 * Super Admin Configuration (Client-Safe)
 *
 * Client-safe version of super admin configuration that doesn't import server-side modules.
 * This file can be safely imported in client components.
 */

/**
 * Get super admin configuration from environment variables (client-safe)
 * This only works with NEXT_PUBLIC_ prefixed environment variables
 */
function getClientSuperAdminEmails(): string[] {
  try {
    // Only NEXT_PUBLIC_ prefixed variables are available on the client
    const superAdminEmailsEnv = process.env.NEXT_PUBLIC_SUPER_ADMIN_EMAILS;
    
    if (!superAdminEmailsEnv) {
      return [];
    }

    // Support both comma-separated and JSON array formats
    if (superAdminEmailsEnv.startsWith('[')) {
      return JSON.parse(superAdminEmailsEnv);
    } else {
      return superAdminEmailsEnv.split(',').map(email => email.trim()).filter(Boolean);
    }
  } catch (error) {
    console.error('Failed to parse client-side super admin emails:', error);
    return [];
  }
}

/**
 * Fallback super admin emails for development/testing (client-safe)
 * These should NOT be used in production
 */
const DEVELOPMENT_FALLBACK_EMAILS = [
  '<EMAIL>',
  '<EMAIL>'
] as const;

/**
 * Check if an email is configured as a super admin email (client-safe version)
 * This version only checks client-accessible environment variables
 * 
 * Note: This is for UI purposes only and should not be relied upon for security.
 * Always use server-side validation for actual authorization decisions.
 */
export function isSuperAdminEmail(email: string | null | undefined): boolean {
  if (!email) return false;

  try {
    const clientEmails = getClientSuperAdminEmails();
    
    // If no client emails configured, check development fallbacks in dev mode
    if (clientEmails.length === 0 && process.env.NODE_ENV === 'development') {
      return DEVELOPMENT_FALLBACK_EMAILS.includes(email as any);
    }
    
    return clientEmails.includes(email);
  } catch (error) {
    console.error('Failed to check super admin email (client-safe):', error);
    return false;
  }
}

/**
 * Get all configured super admin emails (client-safe version)
 * This version only returns client-accessible emails
 * 
 * Note: This is for UI purposes only and may not include all super admin emails.
 * Server-side configuration may include additional emails not visible to the client.
 */
export function getSuperAdminEmails(): string[] {
  try {
    const clientEmails = getClientSuperAdminEmails();
    
    // If no client emails configured, return development fallbacks in dev mode
    if (clientEmails.length === 0 && process.env.NODE_ENV === 'development') {
      return [...DEVELOPMENT_FALLBACK_EMAILS];
    }
    
    return [...clientEmails]; // Return a copy to prevent mutation
  } catch (error) {
    console.error('Failed to get super admin emails (client-safe):', error);
    return [];
  }
}

/**
 * Validate super admin configuration (client-safe version)
 * This only validates client-accessible configuration
 */
export function validateSuperAdminConfig(): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];

  try {
    const emails = getSuperAdminEmails();

    // Check if any emails are configured
    if (emails.length === 0) {
      if (process.env.NODE_ENV === 'production') {
        errors.push('No super admin emails configured for client-side access');
      } else {
        warnings.push('No super admin emails configured for client-side access (using development fallbacks)');
      }
    }

    // Validate email formats
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const invalidEmails = emails.filter(email => !emailRegex.test(email));
    if (invalidEmails.length > 0) {
      errors.push(`Invalid email format(s): ${invalidEmails.join(', ')}`);
    }

    // Warn about client-side limitations
    warnings.push('Client-side super admin configuration may not include all server-side configured emails');
    warnings.push('Use server-side validation for actual authorization decisions');

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  } catch (error) {
    return {
      isValid: false,
      errors: [`Configuration validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`],
      warnings
    };
  }
}

/**
 * Log super admin configuration status (client-safe version)
 */
export function logSuperAdminConfigStatus(): void {
  try {
    const validation = validateSuperAdminConfig();
    const emails = getSuperAdminEmails();

    console.log('Super Admin Configuration Status (Client-Safe):');
    console.log(`  Total Emails: ${emails.length}`);
    console.log(`  Valid: ${validation.isValid}`);

    if (validation.warnings.length > 0) {
      console.warn('Super Admin Configuration Warnings:');
      validation.warnings.forEach(warning => console.warn(`  - ${warning}`));
    }

    if (validation.errors.length > 0) {
      console.error('Super Admin Configuration Errors:');
      validation.errors.forEach(error => console.error(`  - ${error}`));
    }

    // In development, show configured emails (masked)
    if (process.env.NODE_ENV === 'development') {
      console.log('Configured Super Admin Emails (masked):');
      emails.forEach(email => {
        const [local, domain] = email.split('@');
        const maskedLocal = local.length > 2 ? `${local[0]}***${local[local.length - 1]}` : '***';
        console.log(`  - ${maskedLocal}@${domain}`);
      });
    }
  } catch (error) {
    console.error('Failed to log super admin configuration status (client-safe):', error);
  }
}

// Legacy compatibility exports (client-safe versions)
export const SUPER_ADMIN_EMAILS = getSuperAdminEmails();
export type SuperAdminEmail = string;

/**
 * Security note about client-side super admin checking
 */
export const CLIENT_SIDE_SECURITY_NOTE = `
⚠️  SECURITY NOTICE: Client-side super admin checking is for UI purposes only.
Always use server-side validation for actual authorization decisions.
Client-side checks can be bypassed by disabling JavaScript or manipulating client code.
`;

// Log security notice in development
if (process.env.NODE_ENV === 'development') {
  console.warn(CLIENT_SIDE_SECURITY_NOTE);
}
