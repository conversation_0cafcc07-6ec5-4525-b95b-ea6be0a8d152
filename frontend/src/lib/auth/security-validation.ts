/**
 * Security Validation Utilities
 * 
 * Provides comprehensive security validation functions that cannot be bypassed
 * by client-side manipulation. All validations are performed server-side.
 */

import { User } from '@supabase/supabase-js';

/**
 * Security validation result interface
 */
export interface SecurityValidationResult {
  isValid: boolean;
  reason?: string;
  userRole?: string | null;
  isSuperAdmin?: boolean;
  errors: string[];
  warnings: string[];
}

/**
 * Permission validation request interface
 */
export interface PermissionValidationRequest {
  roles?: string[];
  feature?: 'templates' | 'documents' | 'cases' | 'admin';
  requireSuperAdmin?: boolean;
  requireAuthentication?: boolean;
}

/**
 * Validates user permissions using server-side validation
 * This function cannot be bypassed by client-side manipulation
 * 
 * @param request - Permission validation parameters
 * @returns Promise<SecurityValidationResult> - Validation result
 */
export async function validatePermissionsSecurely(
  request: PermissionValidationRequest
): Promise<SecurityValidationResult> {
  const errors: string[] = [];
  const warnings: string[] = [];

  try {
    const response = await fetch('/api/auth/check-permission', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });

    if (!response.ok) {
      if (response.status === 401) {
        return {
          isValid: false,
          reason: 'User not authenticated',
          errors: ['Authentication required'],
          warnings
        };
      }

      if (response.status === 403) {
        const errorData = await response.json().catch(() => ({}));
        return {
          isValid: false,
          reason: errorData.reason || 'Access denied',
          userRole: errorData.userRole,
          isSuperAdmin: errorData.isSuperAdmin,
          errors: ['Access denied'],
          warnings
        };
      }

      throw new Error(`Permission validation failed: ${response.status}`);
    }

    const result = await response.json();

    return {
      isValid: result.hasAccess,
      reason: result.reason,
      userRole: result.userRole,
      isSuperAdmin: result.isSuperAdmin,
      errors,
      warnings
    };

  } catch (error) {
    console.error('Security validation error:', error);
    return {
      isValid: false,
      reason: 'Security validation failed',
      errors: [`Validation error: ${error instanceof Error ? error.message : 'Unknown error'}`],
      warnings
    };
  }
}

/**
 * Validates super admin access using server-side validation
 * This function cannot be bypassed by client-side manipulation
 * 
 * @returns Promise<SecurityValidationResult> - Validation result
 */
export async function validateSuperAdminAccessSecurely(): Promise<SecurityValidationResult> {
  return validatePermissionsSecurely({ requireSuperAdmin: true });
}

/**
 * Validates role-based access using server-side validation
 * This function cannot be bypassed by client-side manipulation
 * 
 * @param allowedRoles - Array of roles that should have access
 * @returns Promise<SecurityValidationResult> - Validation result
 */
export async function validateRoleAccessSecurely(
  allowedRoles: string[]
): Promise<SecurityValidationResult> {
  return validatePermissionsSecurely({ roles: allowedRoles });
}

/**
 * Validates feature-based access using server-side validation
 * This function cannot be bypassed by client-side manipulation
 * 
 * @param feature - Feature to check access for
 * @returns Promise<SecurityValidationResult> - Validation result
 */
export async function validateFeatureAccessSecurely(
  feature: 'templates' | 'documents' | 'cases' | 'admin'
): Promise<SecurityValidationResult> {
  return validatePermissionsSecurely({ feature });
}

/**
 * Client-side security warning utility
 * Logs security warnings for debugging and monitoring
 */
export function logSecurityWarning(
  component: string,
  message: string,
  details?: Record<string, any>
): void {
  const timestamp = new Date().toISOString();
  const logEntry = {
    timestamp,
    component,
    message,
    details,
    type: 'SECURITY_WARNING'
  };

  console.warn(`[SECURITY WARNING] ${component}: ${message}`, details);

  // In production, you might want to send this to a monitoring service
  if (process.env.NODE_ENV === 'production') {
    // Example: Send to monitoring service
    // sendToMonitoringService(logEntry);
  }
}

/**
 * Client-side security error utility
 * Logs security errors for debugging and monitoring
 */
export function logSecurityError(
  component: string,
  message: string,
  error?: Error,
  details?: Record<string, any>
): void {
  const timestamp = new Date().toISOString();
  const logEntry = {
    timestamp,
    component,
    message,
    error: error?.message,
    stack: error?.stack,
    details,
    type: 'SECURITY_ERROR'
  };

  console.error(`[SECURITY ERROR] ${component}: ${message}`, error, details);

  // In production, you might want to send this to a monitoring service
  if (process.env.NODE_ENV === 'production') {
    // Example: Send to monitoring service
    // sendToMonitoringService(logEntry);
  }
}

/**
 * Validates user metadata for security consistency
 * Checks for potential security issues in user metadata
 */
export function validateUserMetadataSecurity(user: User | null): SecurityValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  if (!user) {
    return {
      isValid: false,
      reason: 'No user provided',
      errors: ['User is null or undefined'],
      warnings
    };
  }

  // Check for required metadata fields
  const appMetadata = user.app_metadata || {};
  const userMetadata = user.user_metadata || {};

  // Validate role consistency
  const appRole = appMetadata.role;
  const userRole = userMetadata.role;

  if (appRole && userRole && appRole !== userRole) {
    warnings.push(`Role mismatch: app_metadata.role (${appRole}) != user_metadata.role (${userRole})`);
  }

  // Validate tenant ID consistency
  const appTenantId = appMetadata.tenant_id;
  const userTenantId = userMetadata.tenant_id;

  if (appTenantId && userTenantId && appTenantId !== userTenantId) {
    warnings.push(`Tenant ID mismatch: app_metadata.tenant_id (${appTenantId}) != user_metadata.tenant_id (${userTenantId})`);
  }

  // Check for super admin flag consistency
  const appSuperAdmin = appMetadata.is_super_admin;
  const userSuperAdmin = userMetadata.is_super_admin;

  if (appSuperAdmin !== userSuperAdmin && (appSuperAdmin || userSuperAdmin)) {
    warnings.push(`Super admin flag mismatch: app_metadata (${appSuperAdmin}) != user_metadata (${userSuperAdmin})`);
  }

  // Validate email presence
  if (!user.email) {
    errors.push('User email is missing');
  }

  return {
    isValid: errors.length === 0,
    reason: errors.length > 0 ? errors[0] : undefined,
    userRole: appRole || userRole,
    isSuperAdmin: appSuperAdmin || userSuperAdmin,
    errors,
    warnings
  };
}

/**
 * Security audit utility for component access
 * Logs access attempts for security monitoring
 */
export function auditComponentAccess(
  component: string,
  user: User | null,
  accessGranted: boolean,
  reason?: string
): void {
  const timestamp = new Date().toISOString();
  const auditEntry = {
    timestamp,
    component,
    userId: user?.id,
    userEmail: user?.email,
    userRole: user?.app_metadata?.role || user?.user_metadata?.role,
    accessGranted,
    reason,
    type: 'COMPONENT_ACCESS_AUDIT'
  };

  if (accessGranted) {
    console.log(`[AUDIT] Access granted to ${component}`, auditEntry);
  } else {
    console.warn(`[AUDIT] Access denied to ${component}`, auditEntry);
  }

  // In production, you might want to send this to an audit log service
  if (process.env.NODE_ENV === 'production') {
    // Example: Send to audit service
    // sendToAuditService(auditEntry);
  }
}
