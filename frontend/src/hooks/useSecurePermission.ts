'use client'

import { useState, useEffect, useCallback } from 'react';
import { useUser } from '@/contexts/UserContext';

interface PermissionCheckRequest {
  roles?: string[];
  feature?: 'templates' | 'documents' | 'cases' | 'admin';
  requireSuperAdmin?: boolean;
}

interface PermissionCheckResponse {
  hasAccess: boolean;
  userRole: string | null;
  isSuperAdmin: boolean;
  reason?: string;
}

interface UseSecurePermissionResult {
  hasAccess: boolean;
  isLoading: boolean;
  error: string | null;
  userRole: string | null;
  isSuperAdmin: boolean;
  recheckPermission: () => Promise<void>;
}

/**
 * Secure permission hook that validates permissions server-side
 * 
 * This hook provides defense-in-depth by:
 * 1. Using server-side validation that cannot be bypassed
 * 2. Caching results to avoid excessive API calls
 * 3. Automatically rechecking when user context changes
 * 4. Providing loading states for better UX
 * 
 * @param request - Permission check parameters
 * @returns Permission check result with loading state
 */
export function useSecurePermission(request: PermissionCheckRequest): UseSecurePermissionResult {
  const { user, loading: userLoading } = useUser();
  const [hasAccess, setHasAccess] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [userRole, setUserRole] = useState<string | null>(null);
  const [isSuperAdmin, setIsSuperAdmin] = useState<boolean>(false);

  const checkPermission = useCallback(async () => {
    // Don't check permissions if user is still loading
    if (userLoading) {
      return;
    }

    // If no user, deny access
    if (!user) {
      setHasAccess(false);
      setUserRole(null);
      setIsSuperAdmin(false);
      setError('User not authenticated');
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/auth/check-permission', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      });

      if (!response.ok) {
        throw new Error(`Permission check failed: ${response.status}`);
      }

      const result: PermissionCheckResponse = await response.json();
      
      setHasAccess(result.hasAccess);
      setUserRole(result.userRole);
      setIsSuperAdmin(result.isSuperAdmin);
      
      if (!result.hasAccess && result.reason) {
        setError(result.reason);
      }

    } catch (err) {
      console.error('Error checking permissions:', err);
      setHasAccess(false);
      setError(err instanceof Error ? err.message : 'Permission check failed');
    } finally {
      setIsLoading(false);
    }
  }, [request, user, userLoading]);

  // Check permissions when user or request changes
  useEffect(() => {
    checkPermission();
  }, [checkPermission]);

  return {
    hasAccess,
    isLoading,
    error,
    userRole,
    isSuperAdmin,
    recheckPermission: checkPermission,
  };
}

/**
 * Convenience hook for role-based access control
 */
export function useSecureRoleCheck(allowedRoles: string[]): UseSecurePermissionResult {
  return useSecurePermission({ roles: allowedRoles });
}

/**
 * Convenience hook for feature-based access control
 */
export function useSecureFeatureCheck(feature: 'templates' | 'documents' | 'cases' | 'admin'): UseSecurePermissionResult {
  return useSecurePermission({ feature });
}

/**
 * Convenience hook for secure super admin access control
 * This provides server-side validation that cannot be bypassed by client-side manipulation
 */
export function useSecureSuperAdminCheck(): UseSecurePermissionResult {
  return useSecurePermission({ requireSuperAdmin: true });
}
