'use client'

import { useUser } from '@/contexts/UserContext'
import { isSuperAdminEmail } from '@/lib/auth/super-admin-config-client'

/**
 * Custom hook for role-based access control
 * SECURITY: Uses secure metadata-based approach instead of client-side JWT parsing
 * For enhanced security, use useSecurePermission hook for critical authorization checks
 */
export function useRbac() {
  const { user, role, tenantId, loading } = useUser()

  return {
    /**
     * Check if the current user has any of the specified roles
     */
    hasRole: (roles: string | string[]): boolean => {
      if (loading) return false
      if (!role) return false

      const rolesToCheck = Array.isArray(roles) ? roles : [roles]
      return rolesToCheck.includes(role)
    },

    /**
     * Check if the current user is an admin (partner role)
     */
    isAdmin: (): boolean => {
      return role === 'partner'
    },

    /**
     * Check if the current user is a regular staff member
     */
    isStaff: (): boolean => {
      return ['partner', 'attorney', 'paralegal', 'staff'].includes(role || '')
    },

    /**
     * Check if the current user is a client
     */
    isClient: (): boolean => {
      return role === 'client'
    },

    /**
     * Check if the current user is a super admin
     * SECURITY: Uses secure metadata-based approach instead of client-side JWT parsing
     * For critical super admin checks, use useSecureSuperAdminCheck hook
     */
    isSuperAdmin: (): boolean => {
      if (loading) return false

      // Primary check: centralized superadmin email list
      if (isSuperAdminEmail(user?.email)) {
        return true;
      }

      // Secondary check: metadata-based super admin flag (server-controlled)
      return user?.app_metadata?.is_super_admin === true ||
             user?.user_metadata?.is_super_admin === true
    },

    /**
     * Check if the current user has super admin access
     * Alias for isSuperAdmin for convenience
     * SECURITY: Uses secure metadata-based approach instead of client-side JWT parsing
     */
    hasSuperAdminAccess: (): boolean => {
      if (loading) return false

      // Primary check: centralized superadmin email list
      if (isSuperAdminEmail(user?.email)) {
        return true;
      }

      // Secondary check: metadata-based super admin flag (server-controlled)
      return user?.app_metadata?.is_super_admin === true ||
             user?.user_metadata?.is_super_admin === true
    },

    /**
     * Get the current user's role
     */
    getRole: (): string | null => {
      return role
    },

    /**
     * Get the current tenant ID
     */
    getTenantId: (): string | null => {
      return tenantId
    },

    /**
     * Check if the user data is still loading
     */
    isLoading: (): boolean => {
      return loading
    },

    /**
     * Check if the user has access to a specific feature
     * @param feature The feature to check access for
     */
    hasAccess: (feature: 'templates' | 'documents' | 'cases' | 'admin'): boolean => {
      if (loading || !role) return false

      switch (feature) {
        case 'templates':
          return ['partner', 'attorney', 'staff'].includes(role)
        case 'documents':
          return ['partner', 'attorney', 'paralegal', 'staff', 'client'].includes(role)
        case 'cases':
          return ['partner', 'attorney', 'paralegal', 'staff'].includes(role)
        case 'admin':
          return role === 'partner'
        default:
          return false
      }
    },

    /**
     * DEPRECATED: JWT claims access removed for security
     * Use server-side validation endpoints for secure authorization checks
     */
    getJwtClaims: () => {
      console.warn('getJwtClaims is deprecated for security reasons. Use server-side validation endpoints instead.');
      return null;
    }
  }
}
