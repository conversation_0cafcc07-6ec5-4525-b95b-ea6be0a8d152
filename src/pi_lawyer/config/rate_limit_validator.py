"""
Rate Limiting Configuration Validator

Validates rate limiting configuration for production readiness and security.
"""

import logging
import os
from typing import Any, Dict, List

logger = logging.getLogger(__name__)


class RateLimitValidationResult:
    """Result of rate limiting configuration validation."""
    
    def __init__(self):
        self.is_valid: bool = True
        self.is_production_ready: bool = True
        self.errors: List[str] = []
        self.warnings: List[str] = []
        self.recommendations: List[str] = []
        self.score: float = 10.0
        self.policies_validated: Dict[str, Dict[str, Any]] = {}


def validate_rate_limiting_configuration() -> RateLimitValidationResult:
    """
    Comprehensive validation of rate limiting configuration.
    
    Returns:
        RateLimitValidationResult with validation details
    """
    result = RateLimitValidationResult()
    
    # Check if rate limiting is enabled
    rate_limiting_enabled = os.getenv("ENABLE_RATE_LIMIT", "true").lower() == "true"
    
    if not rate_limiting_enabled:
        result.warnings.append("Rate limiting is disabled via ENABLE_RATE_LIMIT=false")
        result.score -= 2.0
        if os.getenv("NODE_ENV") == "production":
            result.errors.append("Rate limiting should be enabled in production")
            result.is_production_ready = False
            result.score -= 3.0
    
    # Validate individual policies
    policies_to_validate = [
        ("general", "RATE_LIMIT", "RATE_LIMIT_WINDOW", 100, 60),
        ("auth", "RATE_LIMIT_AUTH", "RATE_LIMIT_AUTH_WINDOW", 10, 300),
        ("upload", "RATE_LIMIT_UPLOAD", "RATE_LIMIT_UPLOAD_WINDOW", 20, 300),
        ("ai", "RATE_LIMIT_AI", "RATE_LIMIT_AI_WINDOW", 30, 60),
        ("admin", "RATE_LIMIT_ADMIN", "RATE_LIMIT_ADMIN_WINDOW", 200, 60),
    ]
    
    for policy_name, limit_env, window_env, default_limit, default_window in policies_to_validate:
        policy_result = _validate_policy(
            policy_name, limit_env, window_env, default_limit, default_window
        )
        result.policies_validated[policy_name] = policy_result
        
        # Aggregate results
        if policy_result["errors"]:
            result.errors.extend(policy_result["errors"])
            result.is_valid = False
            result.score -= 1.0
        
        if policy_result["warnings"]:
            result.warnings.extend(policy_result["warnings"])
            result.score -= 0.5
        
        if policy_result["recommendations"]:
            result.recommendations.extend(policy_result["recommendations"])
    
    # Production-specific validations
    if os.getenv("NODE_ENV") == "production":
        _validate_production_requirements(result)
    
    # Security validations
    _validate_security_requirements(result)
    
    # Final score calculation
    result.score = max(0.0, min(10.0, result.score))
    result.is_valid = len(result.errors) == 0
    result.is_production_ready = result.is_valid and result.score >= 8.0
    
    return result


def _validate_policy(
    policy_name: str, 
    limit_env: str, 
    window_env: str, 
    default_limit: int, 
    default_window: int
) -> Dict[str, Any]:
    """
    Validate a specific rate limiting policy.
    
    Args:
        policy_name: Name of the policy
        limit_env: Environment variable for limit
        window_env: Environment variable for window
        default_limit: Default limit value
        default_window: Default window value
        
    Returns:
        Dictionary with validation results
    """
    result = {
        "policy_name": policy_name,
        "errors": [],
        "warnings": [],
        "recommendations": [],
        "configuration": {}
    }
    
    # Get configured values
    try:
        limit = int(os.getenv(limit_env, str(default_limit)))
        window = int(os.getenv(window_env, str(default_window)))
    except ValueError as e:
        result["errors"].append(f"Invalid {policy_name} configuration: {e}")
        return result
    
    result["configuration"] = {
        "limit": limit,
        "window": window,
        "limit_env": limit_env,
        "window_env": window_env,
        "using_defaults": {
            "limit": os.getenv(limit_env) is None,
            "window": os.getenv(window_env) is None
        }
    }
    
    # Validate limit values
    if limit <= 0:
        result["errors"].append(f"{policy_name} limit must be positive, got {limit}")
    elif limit > 10000:
        result["warnings"].append(f"{policy_name} limit is very high ({limit}), consider if this is intentional")
    
    # Validate window values
    if window <= 0:
        result["errors"].append(f"{policy_name} window must be positive, got {window}")
    elif window > 3600:  # 1 hour
        result["warnings"].append(f"{policy_name} window is very long ({window}s), consider if this is intentional")
    
    # Policy-specific validations
    if policy_name == "auth":
        if limit > 50:
            result["warnings"].append(f"Auth policy limit ({limit}) is high, consider security implications")
        if window < 60:
            result["recommendations"].append(f"Auth policy window ({window}s) is short, consider longer window for security")
    
    elif policy_name == "ai":
        if limit > 100:
            result["warnings"].append(f"AI policy limit ({limit}) is high, consider cost implications")
    
    elif policy_name == "upload":
        if limit > 100:
            result["warnings"].append(f"Upload policy limit ({limit}) is high, consider storage implications")
    
    # Check if using defaults in production
    if os.getenv("NODE_ENV") == "production":
        if result["configuration"]["using_defaults"]["limit"]:
            result["recommendations"].append(f"Consider setting {limit_env} explicitly for production")
        if result["configuration"]["using_defaults"]["window"]:
            result["recommendations"].append(f"Consider setting {window_env} explicitly for production")
    
    return result


def _validate_production_requirements(result: RateLimitValidationResult) -> None:
    """
    Validate production-specific rate limiting requirements.
    
    Args:
        result: Validation result to update
    """
    # Check for production-appropriate limits
    auth_limit = int(os.getenv("RATE_LIMIT_AUTH", "10"))
    if auth_limit > 20:
        result.warnings.append(
            f"Auth rate limit ({auth_limit}) may be too permissive for production"
        )
    
    # Check for monitoring capabilities
    if not os.getenv("RATE_LIMIT_MONITORING_ENABLED", "false").lower() == "true":
        result.recommendations.append(
            "Enable rate limiting monitoring for production (RATE_LIMIT_MONITORING_ENABLED=true)"
        )
    
    # Check for cleanup configuration
    cleanup_threshold = int(os.getenv("RATE_LIMIT_CLEANUP_THRESHOLD", "10000"))
    if cleanup_threshold > 50000:
        result.warnings.append(
            f"Rate limit cleanup threshold ({cleanup_threshold}) is very high for production"
        )


def _validate_security_requirements(result: RateLimitValidationResult) -> None:
    """
    Validate security-specific rate limiting requirements.
    
    Args:
        result: Validation result to update
    """
    # Check for DDoS protection
    general_limit = int(os.getenv("RATE_LIMIT", "100"))
    general_window = int(os.getenv("RATE_LIMIT_WINDOW", "60"))
    
    requests_per_minute = (general_limit / general_window) * 60
    if requests_per_minute > 200:
        result.warnings.append(
            f"General rate limit allows {requests_per_minute:.1f} requests/minute, "
            "consider if this provides adequate DDoS protection"
        )
    
    # Check for authentication protection
    auth_limit = int(os.getenv("RATE_LIMIT_AUTH", "10"))
    auth_window = int(os.getenv("RATE_LIMIT_AUTH_WINDOW", "300"))
    
    auth_requests_per_minute = (auth_limit / auth_window) * 60
    if auth_requests_per_minute > 5:
        result.warnings.append(
            f"Auth rate limit allows {auth_requests_per_minute:.1f} requests/minute, "
            "consider stricter limits for brute force protection"
        )


def log_rate_limiting_validation_status() -> None:
    """
    Log comprehensive rate limiting validation status.
    """
    try:
        result = validate_rate_limiting_configuration()
        
        print("=" * 60)
        print("RATE LIMITING CONFIGURATION VALIDATION")
        print("=" * 60)
        print(f"Overall Status: {'✅ VALID' if result.is_valid else '❌ INVALID'}")
        print(f"Production Ready: {'✅ YES' if result.is_production_ready else '❌ NO'}")
        print(f"Security Score: {result.score:.1f}/10.0")
        print()
        
        if result.errors:
            print("🚨 ERRORS:")
            for error in result.errors:
                print(f"  - {error}")
            print()
        
        if result.warnings:
            print("⚠️  WARNINGS:")
            for warning in result.warnings:
                print(f"  - {warning}")
            print()
        
        if result.recommendations:
            print("💡 RECOMMENDATIONS:")
            for rec in result.recommendations:
                print(f"  - {rec}")
            print()
        
        print("📋 POLICY VALIDATION:")
        for policy_name, policy_result in result.policies_validated.items():
            config = policy_result["configuration"]
            status = "✅" if not policy_result["errors"] else "❌"
            print(f"  {status} {policy_name.upper()}: {config['limit']}/{config['window']}s")
        
        print("=" * 60)
        
    except Exception as e:
        logger.error(f"Failed to validate rate limiting configuration: {e}")


def is_rate_limiting_production_ready() -> bool:
    """
    Quick check if rate limiting configuration is production ready.
    
    Returns:
        True if production ready, False otherwise
    """
    try:
        result = validate_rate_limiting_configuration()
        return result.is_production_ready
    except Exception as e:
        logger.error(f"Rate limiting production readiness check failed: {e}")
        return False
