"""
Rate Limiting Monitoring API

Provides endpoints for monitoring rate limiting statistics and health.
"""

import logging
from typing import Any, Dict

from fastapi import APIRouter, Depends, HTTPException, status

from ..api.security.config import verify_super_admin_access
from ..middleware.rate_limit_middleware import RATE_LIMIT_POLICIES, get_rate_limit_stats

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/rate-limit", tags=["rate-limiting"])


@router.get("/stats")
async def get_rate_limiting_stats(
    _: None = Depends(verify_super_admin_access)
) -> Dict[str, Any]:
    """
    Get current rate limiting statistics.
    
    Requires super admin access for security.
    
    Returns:
        Dictionary containing rate limiting statistics and configuration
    """
    try:
        stats = get_rate_limit_stats()
        
        # Add configuration information
        stats["configuration"] = {
            policy_name: {
                "limit": policy_config["limit"],
                "window": policy_config["window"],
                "endpoints": policy_config["endpoints"]
            }
            for policy_name, policy_config in RATE_LIMIT_POLICIES.items()
        }
        
        return stats
        
    except Exception as e:
        logger.error(f"Failed to get rate limiting stats: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve rate limiting statistics"
        )


@router.get("/health")
async def rate_limiting_health_check() -> Dict[str, Any]:
    """
    Health check endpoint for rate limiting system.
    
    Public endpoint that provides basic health information.
    
    Returns:
        Dictionary containing rate limiting health status
    """
    try:
        stats = get_rate_limit_stats()
        
        # Determine health status
        total_entries = stats["total_entries"]
        cleanup_threshold = stats["cleanup_threshold"]
        
        # Health indicators
        memory_usage_ratio = total_entries / cleanup_threshold
        is_healthy = memory_usage_ratio < 0.8  # Alert if > 80% of threshold
        
        health_status = {
            "status": "healthy" if is_healthy else "warning",
            "rate_limiting_enabled": True,
            "total_entries": total_entries,
            "memory_usage_ratio": round(memory_usage_ratio, 3),
            "policies_active": len(stats["policy_breakdown"]),
            "timestamp": stats["timestamp"]
        }
        
        if not is_healthy:
            health_status["warnings"] = [
                f"Rate limit store approaching cleanup threshold ({total_entries}/{cleanup_threshold})"
            ]
        
        return health_status
        
    except Exception as e:
        logger.error(f"Rate limiting health check failed: {e}")
        return {
            "status": "error",
            "rate_limiting_enabled": False,
            "error": "Health check failed",
            "timestamp": None
        }


@router.post("/cleanup")
async def trigger_cleanup(
    _: None = Depends(verify_super_admin_access)
) -> Dict[str, Any]:
    """
    Manually trigger rate limit store cleanup.
    
    Requires super admin access for security.
    
    Returns:
        Dictionary containing cleanup results
    """
    try:
        from ..middleware.rate_limit_middleware import _clean_rate_limit_store
        
        # Get stats before cleanup
        stats_before = get_rate_limit_stats()
        entries_before = stats_before["total_entries"]
        
        # Trigger cleanup
        _clean_rate_limit_store()
        
        # Get stats after cleanup
        stats_after = get_rate_limit_stats()
        entries_after = stats_after["total_entries"]
        
        cleaned_count = entries_before - entries_after
        
        logger.info(f"Manual rate limit cleanup triggered: removed {cleaned_count} entries")
        
        return {
            "status": "success",
            "entries_before": entries_before,
            "entries_after": entries_after,
            "entries_cleaned": cleaned_count,
            "timestamp": stats_after["timestamp"]
        }
        
    except Exception as e:
        logger.error(f"Failed to trigger rate limit cleanup: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to trigger cleanup"
        )


@router.get("/policies")
async def get_rate_limiting_policies() -> Dict[str, Any]:
    """
    Get current rate limiting policies configuration.
    
    Public endpoint that provides policy information for debugging.
    
    Returns:
        Dictionary containing rate limiting policies
    """
    try:
        return {
            "policies": {
                policy_name: {
                    "limit": policy_config["limit"],
                    "window": policy_config["window"],
                    "endpoints": policy_config["endpoints"],
                    "description": _get_policy_description(policy_name)
                }
                for policy_name, policy_config in RATE_LIMIT_POLICIES.items()
            },
            "total_policies": len(RATE_LIMIT_POLICIES),
            "default_policy": "general"
        }
        
    except Exception as e:
        logger.error(f"Failed to get rate limiting policies: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve rate limiting policies"
        )


def _get_policy_description(policy_name: str) -> str:
    """
    Get a human-readable description for a rate limiting policy.
    
    Args:
        policy_name: Name of the policy
        
    Returns:
        Description string
    """
    descriptions = {
        "auth": "Authentication endpoints (login, register, password reset)",
        "upload": "File upload endpoints",
        "ai": "AI/LLM processing endpoints (CopilotKit, chat, generation)",
        "admin": "Administrative endpoints (admin panels, configuration)",
        "general": "General API endpoints (default policy)"
    }
    
    return descriptions.get(policy_name, f"Custom policy: {policy_name}")


# Export the router for inclusion in the main application
__all__ = ["router"]
